<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->text('description');
            $table->string('image_path')->nullable();
            $table->unsignedSmallInteger('transaction_code');
            $table->unsignedBigInteger('target')->nullable();
            $table->date('end_at')->nullable();
            $table->unsignedBigInteger('achievement')->default(0);
            $table->enum('status', ['draft', 'ongoing', 'done', 'archive'])->default('draft');
            $table->timestamps();
        });

        // Create a partial unique index that excludes archived programs
        // This allows transaction codes from archived programs to be reused
        DB::statement(
            "CREATE UNIQUE INDEX programs_transaction_code_active_unique
             ON programs (transaction_code)
             WHERE status != 'archive'"
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};
