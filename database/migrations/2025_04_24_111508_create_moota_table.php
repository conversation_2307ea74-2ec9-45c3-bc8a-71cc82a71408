<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('moota', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('mutation_id')->unique();
            $table->dateTime('date');
            $table->unsignedBigInteger('amount');
            $table->string('type');
            $table->text('note')->nullable();
            $table->string('trx_id')->nullable();
            $table->string('person')->nullable();
            $table->json('contact')->nullable();
            $table->string('status')->nullable();
            $table->json('items')->nullable();
            $table->json('raw_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('moota');
    }
};
