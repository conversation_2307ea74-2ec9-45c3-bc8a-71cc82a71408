<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('moota', function (Blueprint $table) {
            $table->string('mutation_id')->nullable()->change();
            $table->dropUnique(['mutation_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('moota', function (Blueprint $table) {
            $table->string('mutation_id')->nullable(false)->change();
            $table->unique('mutation_id');
        });
    }
};
