<?php

namespace Database\Seeders;

use App\Models\Donation;
use App\Models\Donatur;
use App\Models\Moota;
use App\Models\Program;
use App\Models\User;
use Illuminate\Database\Seeder;

class DonationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get available programs, donaturs, and staff users
        $programs = Program::all();
        $donaturs = Donatur::all();
        $staffUsers = User::where('role', 'staff')->get();

        // If no programs, donaturs, or staff users exist, create some
        if ($programs->isEmpty()) {
            $programs = Program::factory(5)->create();
        }

        if ($donaturs->isEmpty()) {
            $donaturs = Donatur::factory(10)->create();
        }

        if ($staffUsers->isEmpty()) {
            $staffUsers = User::factory(3)->create(['role' => 'staff']);
        }

        // Create 25 donations with proper relationships
        for ($i = 1; $i <= 25; $i++) {
            // Create a Moota record first
            $moota = Moota::create([
                'mutation_id' => 'MUT' . str_pad($i, 8, '0', STR_PAD_LEFT),
                'date' => now()->subDays(rand(1, 30)),
                'amount' => rand(50000, 5000000),
                'type' => 'CR',
                'note' => 'Donation for ' . $programs->random()->name,
                'trx_id' => 'TRX' . str_pad($i, 8, '0', STR_PAD_LEFT),
                'person' => $donaturs->random()->name,
                'contact' => [
                    'name' => $donaturs->random()->name,
                    'email' => 'donor' . $i . '@example.com',
                    'phone' => '08' . rand(1000000000, 9999999999),
                ],
                'status' => 'success',
                'items' => [],
                'raw_data' => [],
                'creation' => rand(0, 1) ? 'manual' : 'webhook',
            ]);

            // Create the donation with relationships
            Donation::create([
                'moota_id' => $moota->id,
                'program_id' => $programs->random()->id,
                'donatur_id' => $donaturs->random()->id,
                'description' => 'Donation #' . $i . ' for program support',
                'proof_image' => null,
                'user_id' => $staffUsers->random()->id,
                'updated_by' => null,
            ]);
        }
    }
}
