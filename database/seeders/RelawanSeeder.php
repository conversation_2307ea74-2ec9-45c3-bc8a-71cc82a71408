<?php

namespace Database\Seeders;

use App\Models\Program;
use App\Models\Relawan;
use Illuminate\Database\Seeder;

class <PERSON>lawanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get programs to associate with volunteers
        $programs = Program::all();

        // Create 15 volunteers
        for ($i = 0; $i < 15; $i++) {
            $socialMedia = [];

            // Randomly add 0-3 social media links
            $socialMediaCount = rand(0, 3);
            for ($j = 0; $j < $socialMediaCount; $j++) {
                $platform = ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com'][rand(0, 3)];
                $socialMedia[] = "https://{$platform}/user" . rand(1000, 9999);
            }

            // Create the relawan
            $relawan = Relawan::create([
                'name' => fake()->name(),
                'email' => fake()->optional(0.8)->safeEmail(),
                'phone' => '62' . rand(812, 854) . rand(1000, 9999) . rand(1000, 9999),
                'address' => fake()->optional(0.6)->address(),
                'description' => fake()->optional(0.5)->paragraph(),
                'social_media' => $socialMedia,
            ]);

            // Randomly assign 0-3 programs to this volunteer
            if ($programs->isNotEmpty()) {
                $programCount = rand(0, 3);
                if ($programCount > 0) {
                    $selectedPrograms = $programs->random($programCount);

                    $programData = [];
                    foreach ($selectedPrograms as $program) {
                        $programData[$program->id] = [
                            'notes' => fake()->optional(0.7)->sentence(),
                        ];
                    }

                    $relawan->programs()->attach($programData);
                }
            }
        }
    }
}
