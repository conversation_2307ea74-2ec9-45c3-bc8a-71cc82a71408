<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Moota;
use Illuminate\Support\Str;

class MootaWebhookSeeder extends Seeder
{
    public function run(): void
    {
        // Generate 25 dummy payloads
        $payload = [];
        for ($i = 1; $i <= 25; $i++) {
            $payload[] = [
                "account_number" => "***********{$i}",
                "date" => now()->subDays($i)->format('Y-m-d H:i:s'),
                "description" => "TRSF E-BANKING CR {$i} MOOTA CO",
                "amount" => rand(10000, 200000),
                "type" => "CR",
                "note" => "Testing webhook moota #{$i}",
                "balance" => rand(100000, ********),
                "created_at" => now()->subDays($i)->format('Y-m-d H:i:s'),
                "updated_at" => now()->subDays($i)->format('Y-m-d H:i:s'),
                "mutation_id" => Str::random(12),
                "token" => Str::random(12),
                "bank_id" => Str::random(12),
                "taggings" => [],
                "bank" => [
                    "corporate_id" => null,
                    "username" => "testing username",
                    "atas_nama" => "moota",
                    "balance" => rand(100000, ********),
                    "account_number" => "************{$i}",
                    "bank_type" => "bri",
                    "pkg" => null,
                    "login_retry" => 0,
                    "date_from" => now()->subDays($i)->format('Y-m-d H:i:s'),
                    "date_to" => now()->subDays($i)->format('Y-m-d H:i:s'),
                    "meta" => [],
                    "interval_refresh" => 15,
                    "next_queue" => now()->subDays($i)->format('Y-m-d H:i:s'),
                    "is_active" => false,
                    "in_queue" => 0,
                    "in_progress" => 0,
                    "is_crawling" => 1,
                    "recurred_at" => now()->subDays($i)->format('Y-m-d H:i:s'),
                    "status" => null,
                    "ip_address" => null,
                    "ip_address_expired_at" => null,
                    "created_at" => now()->subDays($i)->format('Y-m-d H:i:s'),
                    "token" => Str::random(12),
                    "bank_id" => Str::random(12),
                    "label" => "BRI",
                    "last_update" => "",
                    "icon" => ""
                ],
                "account" => [
                    "account_id" => Str::random(12),
                    "username" => "moota",
                    "account_number" => "************{$i}",
                ],
                "payment_detail" => [
                    "trx_id" => Str::random(10),
                    "contact" => [
                        "name" => "moota",
                        "email" => "<EMAIL>",
                        "phone" => "************",
                    ],
                    "status" => 'success',
                    "items" => [],
                ],
            ];
        }

        // Only create Moota records without donations
        // Donations will be created by the DonationSeeder
        foreach ($payload as $data) {
            Moota::create([
                'mutation_id' => $data['mutation_id'],
                'date' => $data['date'],
                'amount' => $data['amount'],
                'type' => $data['type'],
                'note' => $data['note'],
                'trx_id' => $data['payment_detail']['trx_id'] ?? null,
                'person' => $data['account']['username'] ?? null,
                'contact' => $data['payment_detail']['contact'] ?? null,
                'status' => $data['payment_detail']['status'] ?? null,
                'items' => $data['payment_detail']['items'] ?? null,
                'raw_data' => $data,
                'creation' => 'webhook',
            ]);
        }
    }
}
