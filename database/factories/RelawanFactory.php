<?php

namespace Database\Factories;

use App\Models\Program;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Relawan>
 */
class RelawanFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => (string) Str::orderedUuid(),
            'name' => fake()->name(),
            'email' => fake()->optional(0.8)->safeEmail(),
            'phone' => '62' . rand(812, 854) . rand(1000, 9999) . rand(1000, 9999),
            'address' => fake()->optional(0.6)->address(),
            'description' => fake()->optional(0.5)->paragraph(),
            'social_media' => $this->generateSocialMedia(),
            'program_id' => Program::inRandomOrder()->first()?->id,
        ];
    }

    /**
     * Generate random social media links.
     *
     * @return array
     */
    private function generateSocialMedia(): array
    {
        $socialMedia = [];
        $socialMediaCount = rand(0, 3);

        for ($i = 0; $i < $socialMediaCount; $i++) {
            $platform = ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com'][rand(0, 3)];
            $socialMedia[] = "https://{$platform}/user" . rand(1000, 9999);
        }

        return $socialMedia;
    }
}
