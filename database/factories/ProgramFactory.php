<?php

namespace Database\Factories;

use App\Models\Program;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Program>
 */
class ProgramFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => (string) Str::orderedUuid(),
            'name' => fake()->sentence(3),
            'description' => fake()->paragraphs(3, true),
            'image_path' => null,
            'transaction_code' => Program::generateUniqueTransactionCode(),
            'target' => fake()->numberBetween(1000000, 100000000), // Random target between 1M and 100M
            'end_at' => fake()->dateTimeBetween('+1 month', '+1 year'),
            'achievement' => 0, // Always start with 0 achievement
            'status' => fake()->randomElement(['draft', 'ongoing', 'done', 'archive']),
        ];
    }
}
