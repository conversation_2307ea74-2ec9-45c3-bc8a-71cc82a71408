/**
 * Berbagi Panel - JavaScript Utilities
 * Centralized formatting and utility functions
 */

// Only define utilities if they don't already exist
if (typeof window.BerbagiFunctions !== "object") {
  window.BerbagiFunctions = {
    /**
     * Format phone number for display
     * @param {string|null} phone - Phone number to format (stored as: 6283419094290)
     * @returns {string} Formatted phone number or '-' (displayed as: +62 834 1909 4290)
     */
    formatPhoneForDisplay(phone) {
      if (!phone) return "-";

      // Remove all non-numeric characters
      let number = phone.replace(/[^0-9]/g, "");

      // Ensure it starts with 62
      if (!number.startsWith("62")) {
        if (number.startsWith("0")) {
          number = "62" + number.substring(1);
        } else {
          number = "62" + number;
        }
      }

      // Extract the local number (without country code)
      const localNumber = number.substring(2);

      // Format as: +62 8xx xxxx xxxx
      return "+62 " + localNumber.substring(0, 3) + " " + localNumber.substring(3, 7) + " " + localNumber.substring(7);
    },

    /**
     * Format date to Indonesian format
     * @param {string} dateString - Date string to format
     * @returns {string} Formatted date in Indonesian or '-'
     */
    formatDateIndonesian(dateString) {
      if (!dateString) return "-";

      const months = ["Januari", "Februari", "Maret", "April", "Mei", "Juni", "Juli", "Agustus", "September", "Oktober", "November", "Desember"];

      const date = new Date(dateString);
      const day = date.getDate();
      const month = months[date.getMonth()];
      const year = date.getFullYear();

      return `${day} ${month} ${year}`;
    },

    /**
     * Format date from ISO format to YYYY-MM-DD for input[type=date]
     * @param {string} isoDate - ISO date string
     * @returns {string} Date in YYYY-MM-DD format
     */
    formatDateForInput(isoDate) {
      if (!isoDate) return "";
      const date = new Date(isoDate);
      return date.toISOString().split("T")[0]; // Returns YYYY-MM-DD format
    },

    /**
     * Format currency amount to IDR format
     * @param {number} amount - Amount to format
     * @returns {string} Formatted currency string
     */
    formatCurrency(amount) {
      if (!amount && amount !== 0) return "-";
      return "IDR " + new Intl.NumberFormat("id-ID").format(amount);
    },

    /**
     * Format currency input field (removes non-numeric and updates model)
     * @param {HTMLInputElement} input - Input element
     * @param {string} field - Field name to update in the model
     * @param {object} model - The data model object
     */
    formatCurrencyInput(input, field, model) {
      // Format the display value with thousand separators
      const value = input.value.replace(/\D/g, "");
      if (value) {
        // Update the model with the numeric value
        model[field] = parseInt(value, 10);
      } else {
        model[field] = "";
      }
    },

    /**
     * Standardize phone number for storage (backend format)
     * @param {string|null} phone - Phone number to standardize
     * @returns {string|null} Standardized phone number (stored as: 6283419094290)
     */
    standardizePhoneNumber(phone) {
      if (!phone) return null;

      // Remove all non-numeric characters
      let number = phone.replace(/[^0-9]/g, "");

      // Handle different prefixes
      if (number.startsWith("0")) {
        number = "62" + number.substring(1);
      } else if (!number.startsWith("62")) {
        number = "62" + number;
      }

      // Return without '+' prefix for database storage
      return number;
    },

    /**
     * Validate phone number format
     * @param {string} phone - Phone number to validate
     * @returns {boolean} True if valid format
     */
    isValidPhoneNumber(phone) {
      if (!phone) return true; // Allow empty phone numbers
      // Check if it's a valid Indonesian phone number (starts with +62 or 62 or 0)
      const cleanNumber = phone.replace(/[^0-9]/g, "");
      return (phone.trim().startsWith("+62") || cleanNumber.startsWith("62") || cleanNumber.startsWith("0")) && cleanNumber.length >= 10;
    },

    /**
     * Validate email format
     * @param {string} email - Email to validate
     * @returns {boolean} True if valid format
     */
    isValidEmail(email) {
      if (!email) return false;
      return /\S+@\S+\.\S+/.test(email);
    },

    /**
     * Scroll to first error element
     */
    scrollToFirstError() {
      const firstErrorElement = document.querySelector(".text-red-500");
      if (firstErrorElement) {
        firstErrorElement.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    },

    /**
     * Debounce function for search inputs
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },
  };

  // Create global aliases for commonly used functions
  window.formatPhoneForDisplay = window.BerbagiFunctions.formatPhoneForDisplay;
  window.formatDateIndonesian = window.BerbagiFunctions.formatDateIndonesian;
  window.formatDateForInput = window.BerbagiFunctions.formatDateForInput;
  window.formatCurrency = window.BerbagiFunctions.formatCurrency;
  window.formatCurrencyInput = window.BerbagiFunctions.formatCurrencyInput;
}
