@props(['initialValue' => null, 'placeholder' => 'Donatur', 'id' => 'donatur-filter-input'])

<div 
    x-data="{
        searchTerm: '',
        selectedDonatur: null,
        showDropdown: false,
        donaturs: [],
        loading: false,
        
        init() {
            if ('{{ $initialValue }}') {
                this.fetchDonaturById('{{ $initialValue }}');
            }
            
            document.addEventListener('click', (e) => {
                if (!this.$el.contains(e.target)) {
                    this.showDropdown = false;
                }
            });
        },
        
        async fetchDonaturById(donaturId) {
            if (!donaturId) return;
            
            this.loading = true;
            try {
                const response = await fetch(`/donatur/get-donatur/${donaturId}`);
                
                if (response.ok) {
                    const donatur = await response.json();
                    this.selectedDonatur = donatur;
                    this.$dispatch('donatur-selected', { donatur });
                }
            } catch (error) {
                console.error('Error fetching donatur:', error);
            } finally {
                this.loading = false;
            }
        },
        
        async search() {
            if (this.searchTerm.length < 2) return;
            
            this.loading = true;
            try {
                const response = await fetch(`/donatur/search?q=${encodeURIComponent(this.searchTerm)}`);
                
                if (response.ok) {
                    this.donaturs = await response.json();
                    this.showDropdown = true;
                }
            } catch (error) {
                this.donaturs = [];
            } finally {
                this.loading = false;
            }
        },
        
        selectDonatur(donatur) {
            this.selectedDonatur = donatur;
            this.searchTerm = '';
            this.showDropdown = false;
            this.$dispatch('donatur-selected', { donatur });
        },
        
        clearSelection() {
            this.selectedDonatur = null;
            this.searchTerm = '';
            this.showDropdown = false;
            this.$dispatch('donatur-cleared');
        }
    }"
    {{ $attributes->merge(['class' => 'relative']) }}
>
    <div class="relative h-[30px]">
        <input
            x-show="!selectedDonatur"
            type="text"
            id="{{ $id }}"
            x-model="searchTerm"
            @input.debounce.300ms="search()"
            @focus="if(searchTerm.length >= 2) search()"
            placeholder="{{ $placeholder }}"
            class="block w-full h-[30px] px-2 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
        >

        <div
            x-show="selectedDonatur"
            class="flex items-center justify-between h-[30px] px-2 py-1 text-sm rounded-md border border-gray-300 bg-gray-50"
        >
            <span x-text="selectedDonatur ? selectedDonatur.name : ''" class="truncate"></span>
            <button
                @click="clearSelection()"
                type="button"
                class="ml-1 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
                <x-icon name="x" width="16" height="16" class="inline-block" />
            </button>
        </div>
        
        <div 
            x-show="loading"
            class="absolute right-2 top-1/2 transform -translate-y-1/2"
        >
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        </div>
        
        <div 
            x-show="showDropdown && donaturs.length > 0"
            class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-y-auto"
        >
            <ul class="py-1">
                <template x-for="donatur in donaturs" :key="donatur.id">
                    <li 
                        @click="selectDonatur(donatur)"
                        class="px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer"
                    >
                        <span x-text="donatur.name" class="truncate"></span>
                    </li>
                </template>
            </ul>
        </div>
        
        <div 
            x-show="showDropdown && searchTerm && donaturs.length === 0 && !loading"
            class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 py-2 px-3 text-sm text-gray-500"
        >
            No donaturs found
        </div>
    </div>
</div>
