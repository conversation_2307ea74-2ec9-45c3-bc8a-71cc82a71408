@props(['initialValue' => null, 'placeholder' => 'Pilih Penghubung', 'id' => 'penghubung-select-input'])

<div x-data="penghubungSelect({{ json_encode($initialValue) }})" x-init="window._penghubungSelectId = $el.getAttribute('id') || '{{ $id }}'" id="{{ $id }}" {{ $attributes->merge(['class' => 'relative']) }}>
    <div>
        <input 
            type="text" 
            id="{{ $id }}" 
            x-model="searchTerm" 
            @input.debounce.300ms="searchUsers()" 
            placeholder="{{ $placeholder }}" 
            class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300" 
            x-show="!selectedUser || !selectedUser.name" 
        />
        <div x-show="showResults" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
            <template x-for="user in filteredUsers" :key="user.id">
                <button type="button" @click="selectUser(user)" class="w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100">
                    <span x-text="user.name"></span>
                    <span x-show="user.manager" class="text-xs text-gray-500 ml-1" x-text="'(' + user.manager + ')'"></span>
                </button>
            </template>
            <template x-if="searchTerm && filteredUsers.length === 0 && !loading">
                <div class="w-full px-4 py-2 text-sm text-gray-500">
                    No users found
                </div>
            </template>
        </div>
        <div x-show="selectedUser && selectedUser.name" class="flex items-center justify-between w-full px-3 py-2 text-sm rounded-md border border-gray-300 bg-gray-50">
            <div>
                <span x-text="selectedUser ? selectedUser.name : ''"></span>
                <span x-show="selectedUser && selectedUser.manager" class="text-xs text-gray-500 ml-1" x-text="selectedUser && selectedUser.manager ? '(' + selectedUser.manager + ')' : ''"></span>
            </div>
            <button type="button" @click="clearSelection()" class="text-gray-400 hover:text-gray-600">
                <x-icon name="x" width="16" height="16" />
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('penghubungSelect', (initialValue) => ({
        searchTerm: '',
        showResults: false,
        filteredUsers: [],
        selectedUser: null,
        loading: false,
        
        init() {
            if (initialValue && initialValue !== 'null') {
                this.fetchUserById(initialValue);
            }
        },
        
        async fetchUserById(userId) {
            if (!userId) return;
            
            this.loading = true;
            try {
                const response = await fetch(`/donatur/get-user/${userId}`);
                
                if (response.ok) {
                    const user = await response.json();
                    this.selectedUser = user;
                    this.$dispatch('penghubung-selected', { user });
                }
            } catch (error) {
                console.error('Error fetching user:', error);
            } finally {
                this.loading = false;
            }
        },
        
        async searchUsers() {
            if (this.searchTerm.length < 2) {
                this.showResults = false;
                return;
            }
            
            this.loading = true;
            try {
                const response = await fetch(`/users/search?q=${encodeURIComponent(this.searchTerm)}`);
                
                if (response.ok) {
                    this.filteredUsers = await response.json();
                    this.showResults = this.filteredUsers.length > 0;
                }
            } catch (error) {
                console.error('Error searching users:', error);
                this.filteredUsers = [];
                this.showResults = false;
            } finally {
                this.loading = false;
            }
        },
        
        selectUser(user) {
            this.selectedUser = user;
            this.searchTerm = '';
            this.showResults = false;
            this.$dispatch('penghubung-selected', { user });
        },
        
        clearSelection() {
            this.selectedUser = null;
            this.searchTerm = '';
            this.showResults = false;
            this.$dispatch('penghubung-cleared');
        }
    }));
});
</script>
