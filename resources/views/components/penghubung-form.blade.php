@props(['initialValue' => null, 'placeholder' => 'Select Penghubung', 'id' => 'penghubung-form-input'])

<div 
    x-data="penghubungForm('{{ $initialValue }}')"
    x-init="window._penghubungFormId = $el.getAttribute('id') || '{{ $id }}'"
    id="{{ $id }}"
    {{ $attributes->merge(['class' => 'relative']) }}
>
    <!-- Removed the duplicate label here -->
    
    <div class="relative">
        <!-- Search input (shown when no user is selected) -->
        <input
            x-show="!selectedUser"
            type="text"
            id="{{ $id }}"
            x-model="searchTerm"
            @input.debounce.400ms="searchUsers()"
            @focus="if(searchTerm.length >= 2) searchUsers()"
            placeholder="{{ $placeholder }}"
            class="block w-full h-[38px] px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
            x-bind:class="{'border-red-300 focus:ring-red-500': errors && errors.user_id}"
        >

        <!-- Selected user display -->
        <div
            x-show="selectedUser"
            class="flex items-center justify-between w-full h-[38px] px-3 py-2 text-sm rounded-md border border-gray-300 bg-gray-50"
            x-bind:class="{'border-red-300': errors && errors.user_id}"
        >
            <div class="truncate flex-1 mr-1">
                <span x-text="selectedUser ? selectedUser.name : ''" class="truncate"></span>
                <span x-show="selectedUser && selectedUser.manager" class="text-xs text-gray-500 ml-1" x-text="selectedUser && selectedUser.manager ? '(' + selectedUser.manager + ')' : ''"></span>
            </div>
            <button type="button" @click="clearSelection()" class="text-gray-400 hover:text-gray-600 flex-shrink-0">
                <x-icon name="x" width="16" height="16" />
            </button>
        </div>
        
        <!-- Loading indicator -->
        <div x-show="loading" class="absolute right-3 top-2.5">
            <x-icon name="spinner" width="16" height="16" class="animate-spin text-gray-400" />
        </div>
        
        <!-- Results dropdown -->
        <div 
            x-show="showResults" 
            class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"
        >
            <div class="py-1">
                <template x-for="user in filteredUsers" :key="user.id">
                    <button
                        type="button"
                        @click="selectUser(user)"
                        class="w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                    >
                        <div class="flex items-center justify-between">
                            <span x-text="user.name" class="truncate"></span>
                            <span x-show="user.manager" x-text="user.manager" class="text-xs text-gray-500"></span>
                        </div>
                    </button>
                </template>
            </div>
        </div>
    </div>
    
    <!-- Error message -->
    <div x-show="errors && errors.user_id" x-text="errors && errors.user_id ? errors.user_id : ''" class="mt-1 text-sm text-red-500"></div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('penghubungForm', (initialValue) => ({
            searchTerm: '',
            showResults: false,
            filteredUsers: [],
            searchTimeout: null,
            loading: false,
            selectedUser: null,
            errors: {},
            
            init() {
                // Initialize selectedUser if initialValue exists
                if (initialValue && initialValue !== 'null') {
                    this.fetchUserById(initialValue);
                }
                
                // Listen for init-with-user event
                this.$el.addEventListener('init-with-user', (event) => {
                    if (event.detail && event.detail.userId) {
                        this.fetchUserById(event.detail.userId);
                    }
                });
                
                // Listen for set-selected-user event (direct user object)
                this.$el.addEventListener('set-selected-user', (event) => {
                    if (event.detail && event.detail.user) {
                        this.selectedUser = event.detail.user;
                    }
                });
                
                // Listen for reset event
                window.addEventListener('reset-penghubung-form', () => {
                    this.selectedUser = null;
                    this.searchTerm = '';
                    this.showResults = false;
                    this.filteredUsers = [];
                });
                
                // Listen for reset-form event (direct on element)
                this.$el.addEventListener('reset-form', () => {
                    this.selectedUser = null;
                    this.searchTerm = '';
                    this.showResults = false;
                    this.filteredUsers = [];
                });
                
                // Listen for user-cleared event
                this.$el.addEventListener('user-cleared', () => {
                    this.selectedUser = null;
                    this.searchTerm = '';
                    this.showResults = false;
                    this.filteredUsers = [];
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (!this.$el.contains(e.target)) {
                        this.showResults = false;
                    }
                });
                
                // Get errors from parent component if available
                try {
                    if (typeof $data !== 'undefined' && $data.errors) {
                        this.errors = $data.errors;
                    }
                } catch (e) {
                }
            },
            
            async fetchUserById(userId) {
                if (!userId) return;
                
                this.loading = true;
                try {
                    const response = await fetch(`/donatur/get-user/${userId}`);
                    
                    if (response.ok) {
                        this.selectedUser = await response.json();
                    }
                } catch (error) {
                    console.error('Error fetching user:', error);
                } finally {
                    this.loading = false;
                }
            },
            
            searchUsers() {
                if (this.searchTerm.length < 2) {
                    this.showResults = false;
                    return;
                }
                
                // Clear previous timeout
                if (this.searchTimeout) clearTimeout(this.searchTimeout);
                
                // Set a new timeout (300ms debounce)
                this.searchTimeout = setTimeout(async () => {
                    this.loading = true;
                    try {
                        const response = await fetch(`/users/search?q=${encodeURIComponent(this.searchTerm)}`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            credentials: 'same-origin'
                        });
                        
                        if (response.ok) {
                            this.filteredUsers = await response.json();
                            this.showResults = this.filteredUsers.length > 0;
                        } else {
                            console.error('Error searching users:', response.status);
                            this.filteredUsers = [];
                            this.showResults = false;
                        }
                    } catch (error) {
                        console.error('Error searching users:', error);
                        this.filteredUsers = [];
                        this.showResults = false;
                    } finally {
                        this.loading = false;
                    }
                }, 300);
            },
            
            selectUser(user) {
                if (user) {
                    this.selectedUser = user;
                    this.searchTerm = '';
                    this.showResults = false;
                    this.$dispatch('user-selected', { user });
                }
            },
            
            clearSelection() {
                this.selectedUser = null;
                this.searchTerm = '';
                this.showResults = false;
                this.$dispatch('user-cleared');
            }
        }));
    });
</script>
