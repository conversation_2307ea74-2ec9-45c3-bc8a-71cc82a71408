@props(['phone' => null])

@if($phone)
    @php
        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $phone);

        // Ensure it starts with 62
        if (!str_starts_with($number, '62')) {
            if (str_starts_with($number, '0')) {
                $number = '62' . substr($number, 1);
            } else {
                $number = '62' . $number;
            }
        }

        // Extract the local number (without country code)
        $localNumber = substr($number, 2);

        // Format as: +62 8xx xxxx xxxx
        $formattedPhone = '+62 ' . substr($localNumber, 0, 3) . ' ' .
            substr($localNumber, 3, 4) . ' ' .
            substr($localNumber, 7);
    @endphp
    {{ $formattedPhone }}
@else
    -
@endif