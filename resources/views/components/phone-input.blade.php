@props([
    'id' => 'phone',
    'name' => 'phone',
    'value' => '',
    'placeholder' => '+62 8xx xxxx xxxx',
    'required' => false,
    'disabled' => false
])

<input
    type="tel"
    id="{{ $id }}"
    name="{{ $name }}"
    value="{{ $value }}"
    placeholder="{{ $placeholder }}"
    {{ $required ? 'required' : '' }}
    {{ $disabled ? 'disabled' : '' }}
    {{ $attributes->merge(['class' => '']) }}
    x-init="$el.value && formatPhoneNumber($el)"
    @input.debounce.100ms="formatPhoneNumber($el)"
/>

<script>
    // Only define the function if it doesn't already exist
    if (typeof window.formatPhoneNumber !== 'function') {
        window.formatPhoneNumber = function(input) {
            // Skip if input is empty
            if (!input || !input.value) return;
            
            // Store the current cursor position
            const cursorPos = input.selectionStart;
            
            // Store the current value length
            const oldLength = input.value.length;
            
            // Get the current value
            const currentValue = input.value;
            
            // Remove all non-numeric characters except the plus sign at the beginning
            let value = currentValue.replace(/^\+/, '').replace(/\D/g, '');
            
            // Handle different prefixes
            if (value.startsWith('62')) {
                value = value.substring(2);
            } else if (value.startsWith('0')) {
                value = value.substring(1);
            }
            
            // Format as: +62 xxx xxxx xxxx
            let formatted = '';
            if (value.length > 0) {
                formatted = '+62 ';
                formatted += value.substring(0, Math.min(3, value.length));
                if (value.length > 3) {
                    formatted += ' ' + value.substring(3, Math.min(7, value.length));
                }
                if (value.length > 7) {
                    formatted += ' ' + value.substring(7);
                }
            } else {
                formatted = '';
            }
            
            // Only update if the formatted value is different
            if (formatted !== currentValue) {
                input.value = formatted;
                
                // Dispatch a change event to update the model
                input.dispatchEvent(new Event('change', { bubbles: true }));
                
                // Adjust cursor position if value length changed
                if (cursorPos !== null) {
                    const newPos = cursorPos + (formatted.length - oldLength);
                    input.setSelectionRange(newPos, newPos);
                }
            }
        };
    }
</script>
