<form @submit.prevent="applyFilters" class="flex flex-wrap items-center gap-2 mb-6">
    <div class="flex-1 min-w-[120px]">
        <input type="text" x-model="filters.name" placeholder="Name" @keydown.enter="applyFilters"
               class="block w-full px-2 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150">
    </div>
    <div class="flex-1 min-w-[120px]">
        <input type="number" x-model.number="filters.transaction_code" placeholder="Kode Unik" min="0" max="999" step="1" inputmode="numeric" pattern="[0-9]*"
               @keydown.enter="applyFilters"
               @keypress="validateNumberInput"
               class="block w-full px-2 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150">
    </div>
    @if(auth()->user()->isAdmin())
    <div class="flex-1 min-w-[120px]">
        <div class="relative">
            <select x-model="filters.status" @change="applyFilters"
                    class="block w-full pl-2 pr-8 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none bg-white cursor-pointer">
                <option value="">Status</option>
                <option value="draft">Draft</option>
                <option value="ongoing">Ongoing</option>
                <option value="done">Done</option>
                <option value="archive">Archive</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                <x-icon name="chevron-down" width="16" height="16" />
            </div>
        </div>
    </div>
    @endif
    <div>
        <button type="submit" class="flex items-center p-1.5 cursor-pointer bg-blue-500 text-white rounded-md hover:bg-blue-600 shadow-sm transition duration-150">
            <x-icon name="search" width="16" height="16" />
        </button>
    </div>
</form>