<x-modal-dialog :show-variable="'showViewDialog'" :max-width="'lg'" :close-method="'showViewDialog = false'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900">Program Details</h3>
        <button @click="showViewDialog = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    
    <!-- Image (if available) -->
    <div x-show="viewData.image_url" class="mb-4">
        <img :src="viewData.image_url" class="w-full h-48 object-cover rounded-md" alt="Program Banner">
    </div>
    
    <div class="space-y-4">
        <div>
            <label class="block text-sm text-gray-500 mb-1">Name</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.name"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Description</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900 whitespace-pre-line" x-text="viewData.description ? viewData.description : '-'"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Kode Unik</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.transaction_code"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Target</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="formatCurrency(viewData.target)"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Achievement</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="formatCurrency(viewData.achievement)"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">End Date</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.end_at ? formatDateIndonesian(viewData.end_at) : '-'"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Status</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900 capitalize" 
               x-bind:class="{
                   'text-blue-700': viewData.status === 'ongoing',
                   'text-green-700': viewData.status === 'done',
                   'text-gray-700': viewData.status === 'draft',
                   'text-gray-700': viewData.status === 'archive'
               }"
               x-text="viewData.status === 'draft' ? 'Draft' : (viewData.status === 'ongoing' ? 'Ongoing' : (viewData.status === 'done' ? 'Done' : 'Archive'))"></p>
        </div>
    </div>
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100 mt-4">
        @if(auth()->user()->isAdmin())
        <button @click="showViewDialog = false; $nextTick(() => editProgram(viewData.id))" type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150">
            Edit
        </button>
        @endif
    </div>
</x-modal-dialog>
