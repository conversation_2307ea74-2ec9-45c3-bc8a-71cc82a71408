@extends('layouts.app')

@section('title', 'Donatur Management')
@section('panel-type', 'Donatur Panel')

@section('content')
<div x-data="donatursManagement()"
     x-cloak
     @view-donatur.window="viewDonatur($event.detail.id)"
     @edit-donatur.window="editDonatur($event.detail.id)"
     @delete-donatur.window="confirmDelete($event.detail.id)">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Donatur Management</h2>
        <button @click="openCreateDialog()" class="bg-blue-500 text-white text-sm px-4 py-2 rounded-lg hover:bg-blue-600 cursor-pointer">
            New
        </button>
    </div>

    <!-- Filters -->
    @include('dashboard.donaturs.filter')

    <!-- Donaturs Table with Loading Overlay -->
    <div class="relative">
        <div x-show="loading"
             class="absolute inset-0 backdrop-blur-xs flex items-center justify-center z-10">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span class="text-gray-500 text-sm">Loading...</span>
            </div>
        </div>
        <div id="donaturs-table" x-ref="donatursTable">
            @include('dashboard.donaturs.table')
        </div>
    </div>

    <!-- Create/Edit Dialog -->
    @include('dashboard.donaturs.form')

    <!-- View Dialog -->
    @include('dashboard.donaturs.view')

    <!-- Delete Confirmation Modal -->
    <x-delete-confirmation 
        showVariable="showDeleteDialog"
        title="Confirm Deletion"
        message="Are you sure you want to delete this donatur? This action cannot be undone."
        deleteMethod="deleteDonatur()"
        cancelMethod="showDeleteDialog = false"
    />
</div>

<script>
function donatursManagement() {
    return {
        showDialog: false,
        showViewDialog: false,
        dialogTitle: '',
        loading: false,
        currentPageUrl: '',
        formData: {
            id: null,
            name: '',
            email: '',
            phone: '',
            address: '',
            description: '',
            social_media: []
        },
        viewData: {},
        filters: {
            name: '',
            manager: '',
            penghubung: ''
        },
        errors: {},
        socialMediaLinks: [],
        showDeleteDialog: false,
        currentDonaturId: null,

        init() {
            this.setupPagination();
            this.currentPageUrl = window.location.pathname + window.location.search;
            
            // Initialize filters from URL parameters if they exist
            const urlParams = new URLSearchParams(window.location.search);
            this.filters.name = urlParams.get('name') || '';
            this.filters.manager = urlParams.get('manager') || '';
            this.filters.penghubung = urlParams.get('penghubung') || '';
            
            // Listen for penghubung filter events
            window.addEventListener('update-penghubung-filter', (event) => {
                this.filters.penghubung = event.detail.id;
                this.applyFilters();
            });
            
            window.addEventListener('clear-penghubung-filter', () => {
                this.filters.penghubung = '';
                this.applyFilters();
            });
        },
        
        setupPagination() {
            document.addEventListener('click', (e) => {
                const element = e.target.closest('#pagination-links a');
                if (element) {
                    e.preventDefault();
                    this.loadPage(element.href);
                }
            });
        },

        async loadPage(url) {
            try {
                this.loading = true;
                // Store the current page URL
                this.currentPageUrl = url;

                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const html = await response.text();

                if (!document.startViewTransition) {
                    this.$refs.donatursTable.innerHTML = html;
                    Alpine.initTree(this.$refs.donatursTable);
                    return;
                }

                document.startViewTransition(() => {
                    this.$refs.donatursTable.innerHTML = html;
                    Alpine.initTree(this.$refs.donatursTable);
                });
            } catch (error) {
                console.error('Error loading page:', error);
            } finally {
                this.loading = false;
            }
        },

        applyFilters() {
            const url = new URL(window.location.pathname, window.location.origin);
            
            // Clear existing query parameters
            url.searchParams.delete('name');
            url.searchParams.delete('manager');
            url.searchParams.delete('penghubung');
            
            // Add new filter parameters if they have values
            if (this.filters.name) url.searchParams.set('name', this.filters.name);
            if (this.filters.manager) url.searchParams.set('manager', this.filters.manager);
            if (this.filters.penghubung) url.searchParams.set('penghubung', this.filters.penghubung);
            
            this.loadPage(url.toString());
        },

        openCreateDialog() {
            this.dialogTitle = 'New Donatur';
            this.formData = {
                id: null,
                name: '',
                email: '',
                phone: '',
                address: '',
                description: '',
                social_media: []
            };
            this.socialMediaLinks = [];
            this.errors = {};
            this.showDialog = true;
        },

        async viewDonatur(id) {
            try {
                this.loading = true;
                const response = await fetch(`/donatur/${id}`);
                
                if (!response.ok) {
                    throw new Error(`Failed to fetch donatur: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                
                // Ensure social_media is an array
                if (!data.social_media) {
                    data.social_media = [];
                } else if (!Array.isArray(data.social_media)) {
                    try {
                        data.social_media = JSON.parse(data.social_media);
                        if (!Array.isArray(data.social_media)) {
                            data.social_media = [];
                        }
                    } catch (e) {
                        data.social_media = [];
                    }
                }
                
                this.viewData = data;
                this.showViewDialog = true;
            } catch (error) {
                console.error('Error fetching donatur:', error);
                alert('Failed to load donatur details: ' + error.message);
            } finally {
                this.loading = false;
            }
        },

        getManagersText() {
            if (!this.viewData.managers || this.viewData.managers.length === 0) {
                return '-';
            }
            return this.viewData.managers.map(manager => manager.name).join(', ');
        },

        getPenghubungText() {
            if (!this.viewData.penghubung || this.viewData.penghubung.length === 0) {
                return '-';
            }
            return this.viewData.penghubung.map(user => user.name).join(', ');
        },

        async editDonatur(id) {
            this.showViewDialog = false;

            try {
                const response = await fetch(`/donatur/${id}`);
                if (!response.ok) throw new Error('Failed to fetch donatur');

                const data = await response.json();
                this.formData = {
                    id: data.id,
                    name: data.name,
                    email: data.email || '',
                    phone: data.phone || '',
                    address: data.address || '',
                    description: data.description || '',
                    social_media: Array.isArray(data.social_media) ? data.social_media : []
                };

                // Set the social media links
                this.socialMediaLinks = Array.isArray(data.social_media) ? [...data.social_media] : [];

                // Set dialog title and clear errors
                this.dialogTitle = 'Edit Donatur';
                this.errors = {};

                // Show the dialog
                this.showDialog = true;
            } catch (error) {
                console.error('Error fetching donatur:', error);
                alert('Failed to load donatur details');
            }
        },

        closeDialog() {
            this.showDialog = false;
            this.errors = {};
            
            // Reset form data
            this.resetForm();
        },

        validateForm() {
            this.errors = {};
            let isValid = true;

            // Validate required fields
            if (!this.formData.name || this.formData.name.trim() === '') {
                this.errors.name = 'The name field is required.';
                isValid = false;
            }

            // Validate email if provided
            if (this.formData.email && !this.formData.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
                this.errors.email = 'Please enter a valid email address.';
                isValid = false;
            }

            // Validate phone if provided - update the validation to be more permissive
            if (this.formData.phone && this.formData.phone.trim() !== '') {
                // Just check if it starts with +62, which is handled by the phone input component
                if (!this.formData.phone.trim().startsWith('+62')) {
                    this.errors.phone = 'Phone number must start with +62';
                    isValid = false;
                }
            }

            return isValid;
        },

        updateFormData() {
            console.log('Before updateFormData, user_id:', this.formData.user_id);
            
            // Update formData.social_media from socialMediaLinks array
            this.formData.social_media = this.socialMediaLinks.filter(link => link.trim() !== '');
            
            // Make sure user_id is properly set from selectedUser
            if (this.selectedUser) {
                console.log('Setting user_id from selectedUser:', this.selectedUser.id);
                this.formData.user_id = this.selectedUser.id;
            } else {
                console.log('No selectedUser, keeping user_id as:', this.formData.user_id);
            }
            
            // Ensure all form data is properly formatted
            if (this.formData.phone) {
                // Make sure phone is properly formatted
                const input = document.createElement('input');
                input.value = this.formData.phone;
                window.formatPhoneNumber(input);
                this.formData.phone = input.value;
            }
            
            console.log('After updateFormData, user_id:', this.formData.user_id);
            console.log('Updated form data:', this.formData);
        },
        
        async submitForm() {
            // Validate form before submission
            if (!this.validateForm()) {
                window.BerbagiFunctions.scrollToFirstError();
                return;
            }

            this.loading = true;
            this.errors = {};

            // Update formData from component data
            this.updateFormData();

            try {
                let url = '/donatur';
                let method = 'POST';

                if (this.formData.id) {
                    url = `/donatur/${this.formData.id}`;
                    method = 'PUT';
                }

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: JSON.stringify(this.formData)
                });

                const result = await response.json();

                if (!response.ok) {
                    if (response.status === 422) {
                        this.errors = result.errors;
                    } else {
                        throw new Error(result.message || 'An error occurred');
                    }
                } else {
                    this.closeDialog();
                    this.loadPage(this.currentPageUrl);
                    
                    // Reset form data after successful submission
                    if (!this.formData.id) {
                        this.resetForm();
                    }
                }
            } catch (error) {
                console.error('Error submitting form:', error);
                alert('An error occurred while saving the donatur');
            } finally {
                this.loading = false;
            }
        },

        confirmDelete(id) {
            this.currentDonaturId = id;
            this.showDeleteDialog = true;
        },
        
        async deleteDonatur() {
            try {
                const response = await fetch(`/donatur/${this.currentDonaturId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) throw new Error('Failed to delete donatur');

                this.showDeleteDialog = false;
                this.loadPage(this.currentPageUrl);
            } catch (error) {
                console.error('Error deleting donatur:', error);
                alert('Failed to delete donatur');
            }
        },
        
        // User search functionality
        searchTerm: '',
        filteredUsers: [],
        showResults: false,
        searchTimeout: null,
        
        searchUsers(term) {
            if (!term || term.length < 2) {
                this.filteredUsers = [];
                return;
            }
            
            // Clear any existing timeout
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            
            // Set a new timeout (300ms debounce)
            this.searchTimeout = setTimeout(async () => {
                this.loading = true;
                try {
                    const response = await fetch(`/users/search?q=${encodeURIComponent(term)}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        credentials: 'same-origin'
                    });
                    
                    if (response.ok) {
                        this.filteredUsers = await response.json();
                    } else {
                        console.error('Error searching users:', response.status);
                        this.filteredUsers = [];
                    }
                } catch (error) {
                    console.error('Error searching users:', error);
                    this.filteredUsers = [];
                } finally {
                    this.loading = false;
                }
            }, 300);
        },
        
        selectUser(user) {
            if (user) {
                this.selectedUser = user;
                this.formData.user_id = user.id;
                this.searchTerm = '';
                this.showResults = false;
                console.log('Selected user:', user);
                console.log('Updated formData.user_id:', this.formData.user_id);
            }
        },
        
        clearSelection() {
            this.selectedUser = null;
            this.formData.user_id = null;
            console.log('Cleared user selection');
            console.log('Updated formData.user_id:', this.formData.user_id);
        },
        
        async fetchUserById(userId) {
            if (!userId) return;
            
            this.loading = true;
            try {
                const response = await fetch(`/donatur/get-user/${userId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    credentials: 'same-origin'
                });
                
                if (response.ok) {
                    const user = await response.json();
                    this.selectedUser = user;
                    this.formData.user_id = user.id;
                } else {
                    console.error('Error fetching user:', response.status);
                    this.selectedUser = null;
                }
            } catch (error) {
                console.error('Error fetching user:', error);
                this.selectedUser = null;
            } finally {
                this.loading = false;
            }
        },
        editCurrentDonatur() {
            this.showViewDialog = false;
            this.$nextTick(() => this.editDonatur(this.viewData.id));
        },
        resetForm() {
            this.formData = {
                id: null,
                name: '',
                email: '',
                phone: '',
                address: '',
                description: '',
                social_media: []
            };
            this.socialMediaLinks = [];
            this.errors = {};
        }
    };
}
</script>
@endsection
