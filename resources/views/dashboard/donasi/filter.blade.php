<form @submit.prevent="applyFilters" class="flex flex-wrap items-center gap-2 mb-6">
    <style>
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0,0,0,0);
            white-space: nowrap;
            border: 0;
        }
    </style>
    <div class="flex-1 min-w-[120px]">
        <!-- The label is handled inside the donatur-filter component for accessibility -->
        <x-donatur-filter
            id="filter-donatur"
            aria-label="Donatur"
            :initial-value="request()->get('donatur', '')"
            placeholder="Donatur"
            @donatur-selected="filters.donatur = $event.detail.donatur.id; applyFilters()"
            @donatur-cleared="filters.donatur = ''; applyFilters()"
            class="h-[30px]"
        />
    </div>
    <div class="flex-1 min-w-[120px]">
        <!-- The label is handled inside the program-filter component for accessibility -->
        <x-program-filter
            id="filter-program"
            aria-label="Program"
            :initial-value="request()->get('program', '')"
            placeholder="Program"
            @program-selected="filters.program = $event.detail.program.id; applyFilters()"
            @program-cleared="filters.program = ''; applyFilters()"
            class="h-[30px]"
        />
    </div>
    <div class="flex-1 min-w-[120px]">
        <x-penghubung
            :initial-value="request()->get('penghubung')"
            placeholder="Penghubung"
            @penghubung-selected="filters.penghubung = $event.detail.user.id; applyFilters()"
            @penghubung-cleared="filters.penghubung = ''; applyFilters()"
            class="h-[30px]"
        />
    </div>
    <div class="flex-1 min-w-[120px]">
        <label for="filter-manager" class="sr-only">Manager</label>
        <div class="relative">
            <select id="filter-manager" x-model="filters.manager" @change="applyFilters"
                    class="block w-full pl-2 pr-8 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none bg-white cursor-pointer h-[30px]">
                <option value="">All Managers</option>
                <option value="null">No Manager</option>
                <option value="Man 1">Man 1</option>
                <option value="Man 2">Man 2</option>
                <option value="Man 3">Man 3</option>
                <option value="Man 4">Man 4</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                <x-icon name="chevron-down" width="16" height="16" />
            </div>
        </div>
    </div>
    <div class="flex-1 min-w-[120px]">
        <label for="filter-creation" class="sr-only">Creation</label>
        <div class="relative">
            <select id="filter-creation" x-model="filters.creation" @change="applyFilters"
                    class="block w-full pl-2 pr-8 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none bg-white cursor-pointer h-[30px]">
                <option value="">All Creation</option>
                <option value="manual">Manual</option>
                <option value="webhook">Webhook</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                <x-icon name="chevron-down" width="16" height="16" />
            </div>
        </div>
    </div>
    <div>
        <button type="submit" class="flex items-center p-1.5 cursor-pointer bg-blue-500 text-white rounded-md hover:bg-blue-600 shadow-sm transition duration-150 h-[30px]">
            <x-icon name="search" width="16" height="16" />
        </button>
    </div>
</form>
