<x-modal-dialog :show-variable="'showDialog'" :max-width="'md'" :close-method="'closeDialog()'" :title="''">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900" x-text="dialogTitle"></h3>
        <button @click="closeDialog()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    <form @submit.prevent="submitForm()">
        <!-- Add a hidden input for the user ID -->
        <input type="hidden" x-model="formData.id">
        
        <div class="space-y-4">
            <div>
                <label for="name" class="block text-sm text-gray-500 mb-1">Name <span class="text-red-500">*</span></label>
                <input 
                    type="text"
                    id="name"
                    x-model="formData.name"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    :class="[errors.name ? 'border-red-300 focus:ring-red-500' : '', formData.id !== null ? 'bg-gray-50' : '']"
                    :disabled="formData.id !== null"
                >
                <div x-show="errors.name" x-text="errors.name" class="mt-1 text-sm text-red-500"></div>
            </div>
            <div>
                <label for="email" class="block text-sm text-gray-500 mb-1">Email <span class="text-red-500">*</span></label>
                <input 
                    type="email"
                    id="email"
                    x-model="formData.email"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    :class="[errors.email ? 'border-red-300 focus:ring-red-500' : '', formData.id !== null ? 'bg-gray-50' : '']"
                    :disabled="formData.id !== null"
                >
                <div x-show="errors.email" x-text="errors.email" class="mt-1 text-sm text-red-500"></div>
            </div>
            <div>
                <label for="phone" class="block text-sm text-gray-500 mb-1">Phone</label>
                <x-phone-input
                    id="phone"
                    name="phone"
                    x-model="formData.phone"
                    placeholder="+62 8xx xxxx xxxx"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="errors.phone ? 'border-red-300 focus:ring-red-500' : ''"
                />
                <div x-show="errors.phone" x-text="errors.phone" class="mt-1 text-sm text-red-500"></div>
            </div>
            <div>
                <label for="role" class="block text-sm text-gray-500 mb-1">Role <span class="text-red-500">*</span></label>
                <div class="relative">
                    <select 
                        id="role"
                        x-model="formData.role"
                        @change="handleRoleChange()"
                        class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none"
                        :class="errors.role ? 'border-red-300 focus:ring-red-500' : ''"
                    >
                        <option value="user">User</option>
                        <option value="staff">Staff</option>
                        <option value="admin">Admin</option>
                        <option value="banned">Banned</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                        <x-icon name="chevron-down" width="16" height="16" />
                    </div>
                </div>
                <div x-show="errors.role" x-text="errors.role" class="mt-1 text-sm text-red-500"></div>
            </div>
            <div x-show="['staff', 'admin'].includes(formData.role)">
                <label for="manager" class="block text-sm text-gray-500 mb-1">Manager <span class="text-red-500">*</span></label>
                <div class="relative">
                    <select 
                        id="manager"
                        x-model="formData.manager"
                        class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none"
                        :class="errors.manager ? 'border-red-300 focus:ring-red-500' : ''"
                    >
                        <option value="">Select Manager</option>
                        <option value="Man 1">Man 1</option>
                        <option value="Man 2">Man 2</option>
                        <option value="Man 3">Man 3</option>
                        <option value="Man 4">Man 4</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                        <x-icon name="chevron-down" width="16" height="16" />
                    </div>
                </div>
                <div x-show="errors.manager" x-text="errors.manager" class="mt-1 text-sm text-red-500"></div>
            </div>
        </div>
        <div class="mt-4 flex justify-end">
            <button @click="submitForm()" type="button" class="flex px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150" x-bind:disabled="loading">
                <span x-show="!loading">Save</span>
                <span x-show="loading">
                    <x-icon name="spinner" width="16" height="16" class="animate-spin text-white my-1" />
                </span>
            </button>
        </div>
    </form>
</x-modal-dialog>