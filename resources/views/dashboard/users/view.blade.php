<x-modal-dialog :show-variable="'showViewDialog'" :max-width="'md'" :close-method="'showViewDialog = false'" :title="'User Details'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900">User Details</h3>
        <button @click="showViewDialog = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    
    <div class="space-y-4">
        <div>
            <label class="block text-sm text-gray-500 mb-1">Name</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.name || '-'"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Email</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.email || '-'"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Phone</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="formatPhoneForDisplay(viewData.phone) || '-'"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Role</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.role ? (viewData.role.charAt(0).toUpperCase() + viewData.role.slice(1)) : '-'"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Manager</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.manager || '-'"></p>
        </div>
    </div>
    <div class="mt-6 flex justify-end space-x-3">
        <button @click="showViewDialog = false; $nextTick(() => editUser(viewData.id))" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150">
            Edit
        </button>
    </div>
</x-modal-dialog>
