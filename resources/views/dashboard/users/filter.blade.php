<form @submit.prevent="applyFilters" class="flex flex-wrap items-center gap-2 mb-6">
    <div class="flex-1 min-w-[120px]">
        <input type="text" x-model="filters.name" placeholder="Name" @keydown.enter="applyFilters"
               class="block w-full px-2 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150">
    </div>
    <div class="flex-1 min-w-[120px]">
        <div class="relative">
            <select x-model="filters.role" @change="applyFilters"
                    class="block w-full pl-2 pr-8 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none bg-white cursor-pointer">
                <option value="">All Role</option>
                @foreach ($roles as $role)
                    <option value="{{ $role }}">{{ ucfirst($role) }}</option>
                @endforeach
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                <x-icon name="chevron-down" width="16" height="16" />
            </div>
        </div>
    </div>
    <div class="flex-1 min-w-[120px]">
        <div class="relative">
            <select x-model="filters.manager" @change="applyFilters"
                    class="block w-full pl-2 pr-8 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none bg-white cursor-pointer">
                <option value="">All Manager</option>
                <option value="null">No Manager</option>
                <option value="Man 1">Man 1</option>
                <option value="Man 2">Man 2</option>
                <option value="Man 3">Man 3</option>
                <option value="Man 4">Man 4</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                <x-icon name="chevron-down" width="16" height="16" />
            </div>
        </div>
    </div>
    <div>
        <button type="submit" class="flex items-center p-1.5 cursor-pointer bg-blue-500 text-white rounded-md hover:bg-blue-600 shadow-sm transition duration-150">
            <x-icon name="search" width="16" height="16" />
        </button>
    </div>
</form>
