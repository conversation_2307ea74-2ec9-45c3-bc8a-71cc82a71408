<x-modal-dialog :show-variable="'showViewDialog'" :max-width="'lg'" :close-method="'showViewDialog = false'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900">Relawan Details</h3>
        <button @click="showViewDialog = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    
    <!-- Image (if available) -->
    <div x-show="relawan.image_url" class="mb-4">
        <img :src="relawan.image_url" class="w-full h-48 object-cover rounded-md" alt="Relawan Image">
    </div>
    
    <div class="space-y-4">
        <div>
            <label class="block text-sm text-gray-500 mb-1">Name</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="relawan.name || '-'"></p>
        </div>
        
        <div>
            <label class="block text-sm text-gray-500 mb-1">Email</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="relawan.email || '-'"></p>
        </div>
        
        <div>
            <label class="block text-sm text-gray-500 mb-1">Phone</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="formatPhoneForDisplay(relawan.phone) || '-'"></p>
        </div>
        
        <div>
            <label class="block text-sm text-gray-500 mb-1">Address</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="relawan.address || '-'"></p>
        </div>
        
        <div>
            <label class="block text-sm text-gray-500 mb-1">Description</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="relawan.description || '-'"></p>
        </div>
        
        <div>
            <label class="block text-sm text-gray-500 mb-1">Programs</label>
            <div class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900">
                <template x-if="relawan.programs && relawan.programs.length > 0">
                    <ul class="list-disc list-inside">
                        <template x-for="program in relawan.programs" :key="program.id">
                            <li>
                                <span x-text="program.name"></span>
                                <template x-if="program.end_at">
                                    <span class="text-xs text-gray-400 ml-1" x-text="'(' + formatDateIndonesian(program.end_at) + ')'"></span>
                                </template>
                            </li>
                        </template>
                    </ul>
                </template>
                <template x-if="!relawan.programs || relawan.programs.length === 0">
                    <span>-</span>
                </template>
            </div>
        </div>
        
        <div>
            <label class="block text-sm text-gray-500 mb-1">Social Media</label>
            <div class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900">
                <template x-if="relawan.social_media && relawan.social_media.length > 0 && relawan.social_media.some(link => link)">
                    <ul class="list-disc list-inside">
                        <template x-for="(link, index) in relawan.social_media" :key="index">
                            <li x-show="link">
                                <a :href="link" target="_blank" class="text-blue-600 hover:underline" x-text="link"></a>
                            </li>
                        </template>
                    </ul>
                </template>
                <template x-if="!relawan.social_media || relawan.social_media.length === 0 || !relawan.social_media.some(link => link)">
                    <span>-</span>
                </template>
            </div>
        </div>
    </div>
    
    <div class="mt-6 flex justify-end space-x-3">
        @if(auth()->user()->isAdmin())
        <button type="button" @click="showViewDialog = false; $nextTick(() => editRelawan(currentRelawanId))" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150">
            Edit
        </button>
        @endif
    </div>
</x-modal-dialog>
