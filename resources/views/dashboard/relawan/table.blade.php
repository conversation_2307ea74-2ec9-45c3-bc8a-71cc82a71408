<div class="overflow-x-auto bg-white rounded-lg shadow">
    <div id="relawan-table-content">
        <table class="min-w-full bg-white">
            <thead>
                <tr>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program</th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                @forelse ($relawans as $relawan)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                @if($relawan->image_path)
                                <div class="flex-shrink-0 h-10 w-10 mr-3">
                                    <img class="h-10 w-10 rounded-md object-cover" src="{{ Storage::url($relawan->image_path) }}" alt="{{ $relawan->name }}">
                                </div>
                                @endif
                                <div class="text-sm font-medium text-gray-900">{{ $relawan->name }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500">{{ $relawan->email ?: '-' }}</div>
                            <div class="text-sm text-gray-500">
                                <x-phone-display :phone="$relawan->phone" />
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            @if($relawan->programs->count() > 0)
                                <ul class="list-disc list-inside text-sm text-gray-500">
                                    @foreach($relawan->programs as $program)
                                        <li>
                                            <span class="font-medium">{{ $program->name }}</span>
                                            @if($program->end_at)
                                                @php
                                                    $months = [
                                                        'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                                                        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
                                                    ];
                                                    $date = $program->end_at;
                                                    $formattedDate = $date->format('j') . ' ' . $months[$date->format('n') - 1] . ' ' . $date->format('Y');
                                                @endphp
                                                <span class="text-xs text-gray-400 ml-1">({{ $formattedDate }})</span>
                                            @endif
                                        </li>
                                    @endforeach
                                </ul>
                            @else
                                <div class="text-sm text-gray-500">-</div>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <button @click="$dispatch('view-relawan', { id: '{{ $relawan->id }}' })" class="text-blue-600 hover:text-blue-900">
                                    <x-icon name="eye" width="18" height="18" />
                                </button>
                                @if(auth()->user()->isAdmin())
                                <button @click="$dispatch('edit-relawan', { id: '{{ $relawan->id }}' })" class="text-indigo-600 hover:text-indigo-900">
                                    <x-icon name="pencil" width="18" height="18" />
                                </button>
                                <button @click="confirmDelete('{{ $relawan->id }}')" class="text-red-600 hover:text-red-900">
                                    <x-icon name="trash" width="18" height="18" />
                                </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                            No relawan found
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
        
        <!-- Pagination -->
        <div class="mt-1 px-4 py-3 rounded-b-lg" id="pagination-links">
            <x-pagination :paginator="$relawans" />
        </div>
    </div>
</div>
