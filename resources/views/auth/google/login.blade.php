<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="view-transition" content="same-origin">
    <title>Berbagi</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
        <div class="text-center mb-6">
            <img src="{{ asset('images/berbagi-logo.png') }}" alt="BERBAGI Logo" class="w-24 h-24 mx-auto mb-2">
            <h1 class="text-2xl font-bold text-gray-800">Berbagi</h1>
        </div>

        @if (request()->has('error'))
        <div class="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-lg whitespace-nowrap">
            @if (request()->get('error') === 'google-auth-failed')
            Google authentication failed. Please try again.
            @elseif (request()->get('error') === 'account-banned')
            Your account has been banned.
            @else
            An error occurred. Please try again.
            @endif
        </div>
        @endif

        <button onclick="openGoogleLogin()" class="w-full flex items-center justify-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:cursor-pointer hover:bg-gray-50 transition duration-150">
            <x-icon name="google" width="20" height="20" />
            <span class="font-medium">Sign In with Google</span>
        </button>
    </div>

    <script>
        function openGoogleLogin() {
            const width = 500;
            const height = 600;
            const left = (window.innerWidth - width) / 2;
            const top = (window.innerHeight - height) / 2;

            const popup = window.open('/auth/google', 'Google Login',
                `width=${width},height=${height},left=${left},top=${top},toolbar=no,menubar=no`
            );

            // Poll for changes
            const pollTimer = window.setInterval(() => {
                try {
                    // Check if popup was closed
                    if (popup.closed) {
                        window.clearInterval(pollTimer);
                        window.location.reload();
                    }
                } catch (e) {
                    window.clearInterval(pollTimer);
                }
            }, 500);
        }
    </script>
</body>

</html>