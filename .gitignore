# Laravel 12 .gitignore

# Bun
bun.lockb

# Node modules
/node_modules

# Composer vendor directory
/vendor

# Laravel environment files
.env
.env.*.php

# Laravel logs and cache
/storage/*.key
/storage/app/public
/storage/debugbar
/storage/framework/cache
/storage/framework/sessions
/storage/framework/views
/storage/logs

# Laravel Telescope
/storage/telescope

# Laravel Horizon
/horizon

# Cache files
bootstrap/cache/*.php

# IDE / Editor files
.idea
.vscode
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
Thumbs.db

# PHPUnit test results
.phpunit.result.cache

# NPM / Yarn / Vite build artifacts
/public/hot
/public/storage
/public/build
/build

# Misc
Homestead.yaml
Homestead.json
