<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Moota;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MootaWebhookController extends Controller
{
    /**
     * Whitelisted IP address for Moota webhook.
     * Only requests from this IP will be processed.
     *
     * @var array<int, string>
     */
    private const MOOTA_WHITELISTED_IP = ['***************', '127.0.0.1'];

    /**
     * Handle the incoming webhook request from Moota.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function handle(Request $request)
    {
        // Verify the request is coming from the whitelisted Moota IP address
        if (!in_array($request->ip(), self::MOOTA_WHITELISTED_IP)) {
            Log::warning('Moota webhook request from unauthorized IP address', [
                'ip' => $request->ip(),
                'expected_ip' => self::MOOTA_WHITELISTED_IP,
            ]);

            return response()->json(['message' => 'Unauthorized IP address'], 403);
        }

        // Get the request payload
        $payload = $request->getContent();

        // Verify the signature
        if (!$this->verifySignature($request, $payload)) {
            Log::warning('Moota webhook signature verification failed', [
                'headers' => $request->headers->all(),
                'ip' => $request->ip(),
            ]);

            return response()->json(['message' => 'Invalid signature'], 401);
        }

        // Process the webhook data
        try {
            $data = json_decode($payload, true);

            if (!is_array($data) || empty($data)) {
                return response()->json(['message' => 'Invalid payload format'], 400);
            }

            // Process each mutation in the payload
            foreach ($data as $mutation) {
                $this->processMutation($mutation);
            }

            return response()->json(['message' => 'Webhook processed successfully']);
        } catch (\Exception $e) {
            Log::error('Error processing Moota webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json(['message' => 'Error processing webhook'], 500);
        }
    }

    /**
     * Verify the signature of the webhook request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $payload
     * @return bool
     */
    private function verifySignature(Request $request, string $payload): bool
    {
        $signature = $request->header('Signature');

        if (empty($signature)) {
            return false;
        }

        // Get the secret from environment variables
        $secret = config('services.moota.webhook_secret');

        if (empty($secret)) {
            Log::warning('Moota webhook secret is not configured');
            return false;
        }

        // Calculate the expected signature
        $expectedSignature = hash_hmac('sha256', $payload, $secret);

        // Compare the signatures
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Process a single mutation from the webhook payload.
     *
     * @param  array  $mutation
     * @return void
     */
    private function processMutation(array $mutation): void
    {
        // Check if this mutation has already been processed
        $existing = Moota::where('mutation_id', $mutation['mutation_id'] ?? $mutation['token'] ?? null)->first();

        if ($existing) {
            Log::info('Moota mutation already processed', ['mutation_id' => $mutation['mutation_id'] ?? $mutation['token'] ?? null]);
            return;
        }

        // Extract payment detail fields
        $paymentDetail = $mutation['payment_detail'] ?? [];

        // Get contact information as JSON
        $contact = isset($paymentDetail['contact']) ? $paymentDetail['contact'] : null;

        // Extract person name from contact
        $person = null;
        if (is_array($contact) && isset($contact['name'])) {
            $person = $contact['name'];
        }

        // Ensure items is properly formatted as JSON object, not a JSON string
        $items = null;
        if (isset($paymentDetail['items'])) {
            // Store items directly as a PHP array, Laravel will JSON encode it properly
            $items = $paymentDetail['items'];
        }

        try {
            // Create a new record with simplified structure
            Moota::create([
                'mutation_id' => $mutation['mutation_id'] ?? $mutation['token'] ?? Str::random(15),
                'date' => $mutation['date'] ?? now(),
                'amount' => (int) ($mutation['amount'] ?? 0), // Amount is already in the smallest unit
                'type' => $mutation['type'] ?? '',
                'note' => $mutation['note'] ?? null,
                // Extract fields from payment_detail
                'trx_id' => $paymentDetail['trx_id'] ?? null,
                'person' => $person,
                'contact' => $contact,
                'status' => $paymentDetail['status'] ?? null,
                'items' => $items,
                // Store the complete data in raw_data
                'raw_data' => $mutation,
                'creation' => 'webhook',
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating Moota record', [
                'error' => $e->getMessage(),
                'mutation_id' => $mutation['mutation_id'] ?? $mutation['token'] ?? null,
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }

        Log::info('Moota mutation processed', ['mutation_id' => $mutation['mutation_id'] ?? $mutation['token'] ?? null]);
    }
}
