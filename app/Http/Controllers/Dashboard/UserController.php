<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Contracts\View\View as ViewContract;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    private function standardizePhoneNumber(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $phone);

        // Handle different prefixes
        if (str_starts_with($number, '0')) {
            $number = '62' . substr($number, 1);
        } elseif (!str_starts_with($number, '62')) {
            $number = '62' . $number;
        }

        // Return without '+' prefix for database storage (e.g., 6283419094290)
        return $number;
    }

    private function formatPhoneNumberForDisplay(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $phone);

        // Ensure it starts with 62
        if (!str_starts_with($number, '62')) {
            if (str_starts_with($number, '0')) {
                $number = '62' . substr($number, 1);
            } else {
                $number = '62' . $number;
            }
        }

        // Extract the local number (without country code)
        $localNumber = substr($number, 2);

        // Format as: +62 8xx xxxx xxxx
        return '+62 ' . substr($localNumber, 0, 3) . ' ' .
            substr($localNumber, 3, 4) . ' ' .
            substr($localNumber, 7);
    }

    public function index(): ViewContract|string
    {
        // Check if user has permission to manage users
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $query = User::query();

        // Apply filters if they exist
        if (request()->has('name') && !empty(request('name'))) {
            $query->where('name', 'ilike', '%' . request('name') . '%');
        }

        if (request()->has('manager') && !empty(request('manager'))) {
            if (request('manager') === 'null') {
                $query->whereNull('manager');
            } else {
                $query->where('manager', request('manager'));
            }
        }

        if (request()->has('role') && !empty(request('role'))) {
            $query->where('role', request('role'));
        }

        $users = $query->orderBy('id', 'desc')->paginate(10)->withQueryString();

        if (request()->ajax()) {
            return view('dashboard.users.table', compact('users'))->render();
        }

        // Get available roles for the filter dropdown
        $roles = ['admin', 'staff', 'user', 'banned'];

        return view('dashboard.users.index', compact('users', 'roles'));
    }

    public function store(Request $request)
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:255', // Remove the regex validation
            'role' => 'required|in:admin,staff,user,banned',
            'manager' => [
                'nullable',
                'string',
                'max:255',
                function ($attribute, $value, $fail) use ($request) {
                    if (($request->input('role') === 'staff' || $request->input('role') === 'admin') && empty($value)) {
                        $fail('The manager field is required when role is staff or admin.');
                    }
                    if ($request->input('role') !== 'staff' && $request->input('role') !== 'admin' && !empty($value)) {
                        $fail('The manager field should be empty when role is not staff or admin.');
                    }
                },
            ],
        ]);

        // Standardize phone number
        $validated['phone'] = $this->standardizePhoneNumber($validated['phone']);
        $validated['password'] = Hash::make(Str::random(12));

        User::create($validated);

        return response()->json(['message' => 'User created successfully']);
    }

    public function show(User $user)
    {
        /** @var \App\Models\User $currentUser */
        $currentUser = Auth::user();
        if (!$currentUser->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        // Format phone number for display
        $user->phone = $this->formatPhoneNumberForDisplay($user->phone);

        return response()->json($user);
    }

    public function update(Request $request, User $user)
    {
        /** @var \App\Models\User $currentUser */
        $currentUser = Auth::user();
        if (!$currentUser->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'role' => 'required|in:admin,staff,user,banned',
            'phone' => 'nullable|string|max:255', // Remove the regex validation
            'manager' => 'nullable|string|max:255',
        ]);

        // Standardize phone number if provided
        if (!empty($validated['phone'])) {
            $validated['phone'] = $this->standardizePhoneNumber($validated['phone']);
        }

        $user->update($validated);

        return response()->json(['message' => 'User updated successfully']);
    }
}
