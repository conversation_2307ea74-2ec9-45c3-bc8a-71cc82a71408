<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Program;
use App\Models\Relawan;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class RelawanController extends Controller
{
    /**
     * The image service instance.
     */
    protected ImageService $imageService;

    /**
     * Create a new controller instance.
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Standardize a phone number for storage.
     *
     * @param  string|null  $phone
     * @return string|null
     */
    private function standardizePhoneNumber(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $phone);

        // Handle different prefixes
        if (str_starts_with($number, '0')) {
            $number = '62' . substr($number, 1);
        } elseif (!str_starts_with($number, '62')) {
            $number = '62' . $number;
        }

        // Return without '+' prefix for database storage (e.g., 6283419094290)
        return $number;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Relawan::query()->with(['programs' => function ($query) {
            $query->select('programs.id', 'programs.name', 'programs.end_at', 'programs.status');
        }]);

        // Apply filters
        if ($request->filled('name')) {
            $query->where('name', 'ilike', '%' . $request->name . '%');
        }

        if ($request->filled('program')) {
            $query->whereHas('programs', function ($q) use ($request) {
                $q->where('programs.id', $request->program);
            });
        }

        $relawans = $query->orderBy('id', 'desc')->paginate(10);
        $programs = Program::all();

        if ($request->ajax()) {
            return view('dashboard.relawan.table', compact('relawans', 'programs'))->render();
        }

        return view('dashboard.relawan.index', compact('relawans', 'programs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'description' => 'nullable|string',
            'program_ids' => 'nullable|array',
            'program_ids.*' => 'exists:programs,id',
            'social_media' => 'nullable|array',
            'social_media.*' => 'nullable|string|url',
            'image' => 'nullable|image|max:2048', // 2MB max
        ]);

        // Filter out empty social media links
        if (isset($validated['social_media'])) {
            $validated['social_media'] = array_filter($validated['social_media']);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image_path'] = $this->imageService->processAndStoreImage(
                $request->file('image'),
                'relawans'
            );
        }

        // Remove the image field as it's not in the database
        unset($validated['image']);

        // Standardize phone number
        $phone = isset($validated['phone']) ? $this->standardizePhoneNumber($validated['phone']) : null;

        $relawan = Relawan::create([
            'name' => $validated['name'],
            'email' => $validated['email'] ?? null,
            'phone' => $phone,
            'address' => $validated['address'] ?? null,
            'description' => $validated['description'] ?? null,
            'social_media' => $validated['social_media'] ?? [],
            'image_path' => $validated['image_path'] ?? null,
        ]);

        if (isset($validated['program_ids'])) {
            $relawan->programs()->sync($validated['program_ids']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Relawan created successfully',
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Relawan $relawan)
    {
        $relawan->load('programs');

        return response()->json([
            'relawan' => $relawan
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request, Relawan $relawan)
    {
        $relawan->load('programs');
        $relawan->program_ids = $relawan->programs->pluck('id')->toArray();

        return response()->json([
            'relawan' => $relawan
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Relawan $relawan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'description' => 'nullable|string',
            'program_ids' => 'nullable|array',
            'program_ids.*' => 'exists:programs,id',
            'social_media' => 'nullable|array',
            'social_media.*' => 'nullable|string|url',
            'image' => 'nullable|image|max:2048', // 2MB max
        ]);

        // Filter out empty social media links
        if (isset($validated['social_media'])) {
            $validated['social_media'] = array_filter($validated['social_media']);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($relawan->image_path) {
                $this->imageService->deleteImage($relawan->image_path);
            }

            $validated['image_path'] = $this->imageService->processAndStoreImage(
                $request->file('image'),
                'relawans'
            );
        }

        // Remove the image field as it's not in the database
        unset($validated['image']);

        // Standardize phone number
        $phone = isset($validated['phone']) ? $this->standardizePhoneNumber($validated['phone']) : null;

        $relawan->update([
            'name' => $validated['name'],
            'email' => $validated['email'] ?? null,
            'phone' => $phone,
            'address' => $validated['address'] ?? null,
            'description' => $validated['description'] ?? null,
            'social_media' => $validated['social_media'] ?? [],
            'image_path' => $validated['image_path'] ?? $relawan->image_path,
        ]);

        if (isset($validated['program_ids'])) {
            $relawan->programs()->sync($validated['program_ids']);
        } else {
            $relawan->programs()->detach();
        }

        return response()->json([
            'success' => true,
            'message' => 'Relawan updated successfully',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Relawan $relawan)
    {
        // Delete the image if exists
        if ($relawan->image_path) {
            $this->imageService->deleteImage($relawan->image_path);
        }

        $relawan->programs()->detach();
        $relawan->delete();

        return response()->json([
            'success' => true,
            'message' => 'Relawan deleted successfully',
        ]);
    }
}
