<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class PanelController extends Controller
{
    public function index(): View
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        if ($user->isAdmin()) {
            return view('dashboard.admin_panel');
        } elseif ($user->isStaff()) {
            return view('dashboard.staff_panel');
        } elseif ($user->isUser()) {
            return view('dashboard.user_panel');
        } else {
            abort(403, 'Unauthorized action.');
        }
    }
}
