<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Lara<PERSON>\Socialite\Facades\Socialite;

class GoogleController extends Controller
{
    public function login()
    {
        if (Auth::check()) {
            return $this->redirectBasedOnRole();
        }

        return view('auth.google.login');
    }

    public function logout()
    {
        Auth::logout();
        return redirect('/');
    }

    public function redirect()
    {
        return Socialite::driver('google')->redirect();
    }

    public function callback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            $user = User::updateOrCreate(
                ['email' => $googleUser->email],
                [
                    'name' => $googleUser->name,
                    'google_id' => $googleUser->id,
                    'password' => bcrypt(Str::random(32)),
                ]
            );

            Auth::login($user);

            return '
                <script>
                    if (window.opener) {
                        window.opener.location.reload();
                        window.close();
                    } else {
                        window.location.href = "/";
                    }
                </script>
            ';
        } catch (\Exception $e) {
            return '
                <script>
                    if (window.opener) {
                        window.opener.location.href = "/?error=google-auth-failed";
                        window.close();
                    } else {
                        window.location.href = "/?error=google-auth-failed";
                    }
                </script>
            ';
        }
    }

    private function redirectBasedOnRole()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        if ($user->isBanned()) {
            Auth::logout();
            return '
                <script>
                    if (window.opener) {
                        window.opener.location.href = "/?error=account-banned";
                        window.close();
                    } else {
                        window.location.href = "/?error=account-banned";
                    }
                </script>
            ';
        }

        // All roles now use the same panel URL
        return redirect('/panel');
    }
}
