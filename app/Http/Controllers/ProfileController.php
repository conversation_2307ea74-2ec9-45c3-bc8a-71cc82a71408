<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ProfileController extends Controller
{
    /**
     * Get the authenticated user's profile.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        // Format phone number for display if needed
        if ($user->phone) {
            $user->phone = $this->formatPhoneNumberForDisplay($user->phone);
        }

        return response()->json($user);
    }

    /**
     * Update the authenticated user's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        // Log the request for debugging
        Log::info('Profile update request', [
            'method' => $request->method(),
            'all' => $request->all(),
            'content' => $request->getContent(),
        ]);

        // Validate the request
        $rules = [
            'name' => 'required|string|max:255',
            'phone' => ['nullable', 'string', 'max:255'],
        ];

        // Add manager validation only for staff role
        if ($user->isStaff()) {
            $rules['manager'] = 'nullable|string|max:255';
        }

        $validated = $request->validate($rules);

        // Standardize phone number if provided
        if (!empty($validated['phone'])) {
            $validated['phone'] = $this->standardizePhoneNumber($validated['phone']);
        }

        // Update user
        $user->update($validated);

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $user
        ]);
    }

    /**
     * Format a phone number for display.
     *
     * @param  string|null  $phone
     * @return string|null
     */
    private function formatPhoneNumberForDisplay(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $phone);

        // Ensure it starts with 62
        if (!str_starts_with($number, '62')) {
            if (str_starts_with($number, '0')) {
                $number = '62' . substr($number, 1);
            } else {
                $number = '62' . $number;
            }
        }

        // Extract the local number (without country code)
        $localNumber = substr($number, 2);

        // Format as: +62 8xx xxxx xxxx
        return '+62 ' . substr($localNumber, 0, 3) . ' ' .
            substr($localNumber, 3, 4) . ' ' .
            substr($localNumber, 7);
    }

    /**
     * Standardize a phone number for storage.
     *
     * @param  string|null  $phone
     * @return string|null
     */
    private function standardizePhoneNumber(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $phone);

        // Handle different prefixes
        if (str_starts_with($number, '0')) {
            $number = '62' . substr($number, 1);
        } elseif (!str_starts_with($number, '62')) {
            $number = '62' . $number;
        }

        // Return without '+' prefix for database storage (e.g., 6283419094290)
        return $number;
    }
}
