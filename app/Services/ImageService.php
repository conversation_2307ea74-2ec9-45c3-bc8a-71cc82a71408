<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageService
{
    /**
     * Maximum dimension (width or height) for resized images
     */
    protected int $maxDimension = 1500;

    /**
     * The image manager instance
     */
    protected ImageManager $manager;

    /**
     * Create a new ImageService instance.
     */
    public function __construct()
    {
        $this->manager = new ImageManager(new Driver());
    }

    /**
     * Process and store an uploaded image.
     *
     * @param UploadedFile $file The uploaded file
     * @param string $path The storage path
     * @param string $disk The storage disk
     * @return string The path to the stored image
     */
    public function processAndStoreImage(UploadedFile $file, string $path, string $disk = 'public'): string
    {
        // Create a unique filename
        $filename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension();
        $storedFilename = $filename . '_' . time() . '.' . $extension;
        $fullPath = $path . '/' . $storedFilename;

        // Load the image
        $image = $this->manager->read($file);

        // Resize the image while maintaining aspect ratio if needed
        $width = $image->width();
        $height = $image->height();

        if ($width > $this->maxDimension || $height > $this->maxDimension) {
            if ($width > $height) {
                $image->scale(width: $this->maxDimension);
            } else {
                $image->scale(height: $this->maxDimension);
            }
        }

        // Convert to a stream for storage
        $imageStream = $image->toJpeg()->toFilePointer();

        // Store the image
        Storage::disk($disk)->put($fullPath, $imageStream);

        // Close the file pointer
        if (is_resource($imageStream)) {
            fclose($imageStream);
        }

        return $fullPath;
    }

    /**
     * Delete an image from storage.
     *
     * @param string|null $path The path to the image
     * @param string $disk The storage disk
     * @return bool True if the image was deleted, false otherwise
     */
    public function deleteImage(?string $path, string $disk = 'public'): bool
    {
        if (!$path) {
            return false;
        }

        return Storage::disk($disk)->delete($path);
    }

    /**
     * Set the maximum dimension for resized images.
     *
     * @param int $maxDimension The maximum dimension
     * @return $this
     */
    public function setMaxDimension(int $maxDimension): self
    {
        $this->maxDimension = $maxDimension;
        return $this;
    }
}
