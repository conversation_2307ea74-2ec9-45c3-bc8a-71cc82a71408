<?php

namespace App\Models;

use App\Models\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Donation extends Model
{
    use HasFactory, HasOrderedUuid;

    protected $fillable = [
        'moota_id',
        'program_id',
        'donatur_id',
        'description',
        'proof_image',
        'user_id',
        'updated_by',
    ];

    /**
     * Get the moota record associated with the donation.
     */
    public function moota(): BelongsTo
    {
        return $this->belongsTo(Moota::class);
    }

    /**
     * Get the program associated with the donation.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the donatur associated with the donation.
     */
    public function donatur(): BelongsTo
    {
        return $this->belongsTo(Donatur::class);
    }

    /**
     * Get the user who updated the donation.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the staff/penghubung associated with the donation.
     */
    public function staff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
