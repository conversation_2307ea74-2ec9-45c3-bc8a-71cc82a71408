<?php

namespace App\Models;

use App\Models\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, HasOrderedUuid, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'role',
        'phone',
        'manager',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function isStaff(): bool
    {
        return $this->role === 'staff';
    }

    public function isUser(): bool
    {
        return $this->role === 'user';
    }

    public function isBanned(): bool
    {
        return $this->role === 'banned';
    }

    /**
     * Get the donaturs associated with the user through donations.
     */
    public function donaturs()
    {
        return $this->belongsToMany(Donatur::class, 'donations', 'user_id', 'donatur_id');
    }

    /**
     * Get the donations updated by this user.
     */
    public function updatedDonations()
    {
        return $this->hasMany(Donation::class, 'updated_by');
    }

    /**
     * Get the donations staffed by this user.
     */
    public function staffedDonations()
    {
        return $this->hasMany(Donation::class, 'user_id');
    }
}
