<?php

namespace App\Models;

use App\Models\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Moota extends Model
{
    use HasFactory, HasOrderedUuid;

    protected $table = 'moota';

    protected $fillable = [
        'mutation_id',
        'date',
        'amount',
        'type',
        'note',
        'trx_id',
        'person',
        'contact',
        'status',
        'items',
        'raw_data',
        'creation',
    ];

    protected function casts(): array
    {
        return [
            'date' => 'datetime',
            'contact' => 'json',
            'items' => 'json',
            'raw_data' => 'json',
        ];
    }

    /**
     * Get the donations associated with this moota record.
     */
    public function donations()
    {
        return $this->hasMany(Donation::class);
    }
}
