<?php

namespace App\Models;

use App\Models\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Donatur extends Model
{
    use HasFactory, HasOrderedUuid;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'description',
        'social_media',
    ];

    protected $casts = [
        'social_media' => 'array',
    ];

    /**
     * Get the donations associated with the donatur.
     */
    public function donations()
    {
        return $this->hasMany(Donation::class);
    }

    /**
     * Get the penghubung (staff) associated with the donatur through donations.
     */
    public function penghubung()
    {
        return $this->belongsToMany(User::class, 'donations', 'donatur_id', 'user_id')
            ->distinct();
    }

    /**
     * Get the managers associated with the donatur through donations.
     */
    public function managers()
    {
        return $this->belongsToMany(User::class, 'donations', 'donatur_id', 'manager_id')
            ->distinct();
    }

    /**
     * Get unique manager names from the donations.
     */
    public function getManagersAttribute()
    {
        return $this->managers()
            ->whereNotNull('name')
            ->get()
            ->pluck('name')
            ->unique()
            ->values();
    }
}
