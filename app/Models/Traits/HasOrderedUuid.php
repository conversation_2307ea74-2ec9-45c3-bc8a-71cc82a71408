<?php

namespace App\Models\Traits;

use Illuminate\Support\Str;

trait HasOrderedUuid
{
    protected static function bootHasOrderedUuid()
    {
        static::creating(function ($model) {
            if (!$model->id) {
                $model->id = (string) Str::orderedUuid();
            }
        });
    }

    public function getIncrementing()
    {
        return false;
    }

    public function getKeyType()
    {
        return 'string';
    }
}
