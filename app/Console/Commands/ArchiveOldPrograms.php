<?php

namespace App\Console\Commands;

use App\Models\Program;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ArchiveOldPrograms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:archive-old-programs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Archive programs that have been in done status for more than 6 months';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Calculate the date 6 months ago
        $threeMonthsAgo = Carbon::now()->subMonths(6);

        // Find all 'done' programs where end_at is more than 6 months ago
        $programs = Program::where('status', 'done')
            ->whereNotNull('end_at')
            ->where('end_at', '<', $threeMonthsAgo->format('Y-m-d'))
            ->get();

        $count = $programs->count();

        if ($count > 0) {
            // Update the status of these programs to 'archive'
            foreach ($programs as $program) {
                $program->status = 'archive';
                $program->save();

                $this->info("Program '{$program->name}' (ID: {$program->id}) status updated from 'done' to 'archive'.");
                Log::info("Program '{$program->name}' (ID: {$program->id}) status automatically updated from 'done' to 'archive' because it was completed more than 6 months ago (end date: {$program->end_at->format('Y-m-d')}).");
            }

            $this->info("Successfully archived {$count} program(s).");
        } else {
            $this->info('No programs need to be archived.');
        }

        return Command::SUCCESS;
    }
}
