<?php

namespace App\Console\Commands;

use App\Models\Program;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateProgramStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-program-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update program status from ongoing to done if end date has passed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = Carbon::today();

        // Find all ongoing programs where end_at is in the past
        $programs = Program::where('status', 'ongoing')
            ->whereNotNull('end_at')
            ->where('end_at', '<', $today)
            ->get();

        $count = $programs->count();

        if ($count > 0) {
            // Update the status of these programs to 'done'
            foreach ($programs as $program) {
                $program->status = 'done';
                $program->save();

                $this->info("Program '{$program->name}' (ID: {$program->id}) status updated from 'ongoing' to 'done'.");
                Log::info("Program '{$program->name}' (ID: {$program->id}) status automatically updated from 'ongoing' to 'done' because end date ({$program->end_at->format('Y-m-d')}) has passed.");
            }

            $this->info("Successfully updated {$count} program(s) from 'ongoing' to 'done'.");
        } else {
            $this->info('No programs need to be updated.');
        }

        return Command::SUCCESS;
    }
}
