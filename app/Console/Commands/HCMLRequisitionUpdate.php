<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class HCMLRequisitionUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:requisition-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Query a table and get all results';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Get all records from the table
            $requisitions = DB::table("requisition")
                ->where("isAdditionalProcPlan", false)
                ->where("procPlanDetailId", "!=", "")
                ->whereNotNull("procPlanDetailId")
                ->get();

            $idProcplans = [];

            $requisitions->each(function ($item) use (&$idProcplans) {
                $idProcplans[] = $item->procPlanDetailId;

                if ($item->procPlanDetails) {
                    $procPlanDetails = json_decode($item->procPlanDetails, true);

                    foreach ($procPlanDetails as $value) {
                        $idProcplans[] = $value['value'];
                    }
                }
            });

            $procplans = DB::table("proc_plan_detail")
                ->whereIn("id", $idProcplans)
                ->where("procPlanCode", "!=", "")
                ->whereNotNull("procPlanCode")
                ->get();

            $requisitions->each(function ($item) use ($procplans) {
                $update = [];

                $procplan = $procplans->firstWhere("id", $item->procPlanDetailId);

                if ($procplan) {
                    $update["procPlanDetailCode"] = $procplan->procPlanCode;
                    $update["procPlanCurrency"] = $procplan->currency;
                    $update["procPlanValue"] = $procplan->valueEstimation;
                } else {
                    $update["procPlanDetailCode"] = $item->procPlanDetailCode;
                    $update["procPlanCurrency"] =  $item->procPlanCurrency;
                    $update["procPlanValue"] = $item->procPlanValue;
                }

                if ($item->procPlanDetails) {
                    $procPlanDetails = json_decode($item->procPlanDetails, true);
                    $ppds = [];

                    foreach ($procPlanDetails as $value) {
                        $procplan = $procplans->firstWhere("id", $value['value']);

                        if ($procplan) {
                            $ppds[] = [
                                "fill" => (bool) $value['fill'],
                                "label" => $procplan->procPlanCode,
                                "value" => $value['value'],
                            ];
                        } else {
                            $ppds[] = $value;
                        }
                    }

                    $update["procPlanDetails"] = json_encode($ppds);
                } else {
                    $update["procPlanDetails"] = json_encode([
                        [
                            "fill" => true,
                            "label" => $procplan->procPlanCode,
                            "value" => $item->procPlanDetailId,
                        ],
                    ]);
                }

                print_r([
                    "id" => $item->id,
                    "procPlanDetailCode" => $item->procPlanDetailCode,
                    "procPlanDetails" => $item->procPlanDetails,
                ]);
                print_r($update);
                print_r('----------');

                // update db requisition item
                DB::table("requisition")->where("id", $item->id)->update([
                    "procPlanDetailCode" => $update["procPlanDetailCode"],
                    "procPlanCurrency" => $update["procPlanCurrency"],
                    "procPlanValue" => $update["procPlanValue"],
                    "procPlanDetails" => $update["procPlanDetails"],
                ]);
            });
        } catch (\Exception $e) {
            $this->error("Error querying table: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
