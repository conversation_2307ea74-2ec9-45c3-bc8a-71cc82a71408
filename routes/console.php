<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

/*
|--------------------------------------------------------------------------
| Schedule Commands
|--------------------------------------------------------------------------
|
| Here you may define all of your scheduled tasks that should run on a
| regular schedule. These tasks will be run using the Laravel scheduler
| which will execute them based on the schedule you define.
|
*/

// Update program status from ongoing to done if end date has passed
Schedule::command('app:update-program-status')
    ->dailyAt('00:00')
    ->description('Update program status from ongoing to done if end date has passed')
    ->appendOutputTo(storage_path('logs/scheduler.log'));

// Archive programs that have been in done status for more than 3 months
Schedule::command('app:archive-old-programs')
    ->dailyAt('00:30')
    ->description('Archive programs that have been in done status for more than 3 months')
    ->appendOutputTo(storage_path('logs/scheduler.log'));
